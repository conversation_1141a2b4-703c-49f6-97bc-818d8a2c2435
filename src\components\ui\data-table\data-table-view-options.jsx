"use client";

import { DropdownMenuTrigger } from "@radix-ui/react-dropdown-menu";
import { Settings2 } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";

export function DataTableViewOptions({ table }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="ml-auto hidden h-8 lg:flex"
        >
          <Settings2 className="mr-2 h-4 w-4" />
          View
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[150px]">
        <DropdownMenuLabel>Toggle columns</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {table
          .getAllColumns()
          .filter(
            (column) =>
              (typeof column.accessorFn !== "undefined" ||
                typeof column.accessorKey !== "undefined") &&
              column.getCanHide()
          )
          .map((column) => {
            // Get a better display name for the column
            const getColumnDisplayName = (column) => {
              // If column has a custom header with title, try to extract it
              if (
                column.columnDef.header &&
                typeof column.columnDef.header === "function"
              ) {
                // For columns with DataTableColumnHeader, we can't easily extract the title
                // So we'll use a mapping based on column.id
                const titleMap = {
                  name: "Name",
                  address: "Address",
                  description: "Description",
                  actions: "Actions",
                };
                return titleMap[column.id] || column.id;
              }
              return column.id;
            };

            return (
              <DropdownMenuCheckboxItem
                key={column.id}
                className="capitalize"
                checked={column.getIsVisible()}
                onCheckedChange={(value) => {
                  console.log(
                    `Toggling column ${column.id} visibility to:`,
                    value
                  );
                  column.toggleVisibility(!!value);
                }}
              >
                {getColumnDisplayName(column)}
              </DropdownMenuCheckboxItem>
            );
          })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
