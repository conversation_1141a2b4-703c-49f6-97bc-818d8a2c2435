import React, { createContext, useContext, useState, useEffect } from 'react';
import { toast } from 'sonner';

const ProjectDetailContext = createContext();

export function useProjectDetail() {
  const context = useContext(ProjectDetailContext);
  if (!context) {
    throw new Error('useProjectDetail must be used within a ProjectDetailProvider');
  }
  return context;
}

export function ProjectDetailProvider({ children }) {
  const [selectedProject, setSelectedProject] = useState(null);
  const [activeTab, setActiveTab] = useState('lighting');
  const [projectItems, setProjectItems] = useState({
    lighting: [],
    aircon: [],
    unit: [],
    curtain: [],
    scene: []
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load all items for a project
  const loadProjectItems = async (projectId) => {
    if (!projectId) return;

    try {
      setLoading(true);
      setError(null);

      const [lighting, aircon, unit, curtain, scene] = await Promise.all([
        window.electronAPI.lighting.getAll(projectId),
        window.electronAPI.aircon.getAll(projectId),
        window.electronAPI.unit.getAll(projectId),
        window.electronAPI.curtain.getAll(projectId),
        window.electronAPI.scene.getAll(projectId)
      ]);

      setProjectItems({
        lighting,
        aircon,
        unit,
        curtain,
        scene
      });
    } catch (err) {
      console.error('Failed to load project items:', err);
      setError('Failed to load project items');
      toast.error('Failed to load project items');
    } finally {
      setLoading(false);
    }
  };

  // Select a project and load its items
  const selectProject = async (project) => {
    setSelectedProject(project);
    if (project) {
      await loadProjectItems(project.id);
    } else {
      setProjectItems({
        lighting: [],
        aircon: [],
        unit: [],
        curtain: [],
        scene: []
      });
    }
  };

  // Generic item operations
  const createItem = async (category, itemData) => {
    if (!selectedProject) return;

    try {
      const newItem = await window.electronAPI[category].create(selectedProject.id, itemData);
      setProjectItems(prev => ({
        ...prev,
        [category]: [newItem, ...prev[category]]
      }));
      toast.success(`${category} item created successfully`);
      return newItem;
    } catch (err) {
      console.error(`Failed to create ${category} item:`, err);
      toast.error(`Failed to create ${category} item`);
      throw err;
    }
  };

  const updateItem = async (category, id, itemData) => {
    try {
      const updatedItem = await window.electronAPI[category].update(id, itemData);
      setProjectItems(prev => ({
        ...prev,
        [category]: prev[category].map(item => 
          item.id === id ? updatedItem : item
        )
      }));
      toast.success(`${category} item updated successfully`);
      return updatedItem;
    } catch (err) {
      console.error(`Failed to update ${category} item:`, err);
      toast.error(`Failed to update ${category} item`);
      throw err;
    }
  };

  const deleteItem = async (category, id) => {
    try {
      await window.electronAPI[category].delete(id);
      setProjectItems(prev => ({
        ...prev,
        [category]: prev[category].filter(item => item.id !== id)
      }));
      toast.success(`${category} item deleted successfully`);
    } catch (err) {
      console.error(`Failed to delete ${category} item:`, err);
      toast.error(`Failed to delete ${category} item`);
      throw err;
    }
  };

  // Reload items when selected project changes
  useEffect(() => {
    if (selectedProject) {
      loadProjectItems(selectedProject.id);
    }
  }, [selectedProject]);

  const value = {
    selectedProject,
    activeTab,
    setActiveTab,
    projectItems,
    loading,
    error,
    selectProject,
    loadProjectItems,
    createItem,
    updateItem,
    deleteItem,
  };

  return (
    <ProjectDetailContext.Provider value={value}>
      {children}
    </ProjectDetailContext.Provider>
  );
}
